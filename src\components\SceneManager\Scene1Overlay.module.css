@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-Thin.woff2') format('woff2');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-ExtraLight.woff2') format('woff2');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-Light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-SemiBold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-ExtraBold.woff2') format('woff2');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-Black.woff2') format('woff2');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow-y: auto;
  z-index: 10;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  font-family: 'Inter', Arial, Helvetica, sans-serif;
  pointer-events: none;
}

.topNavBar {
  width: 100%;
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 2rem 3.5rem 0 2.5rem;
  z-index: 20;
  pointer-events: auto;
  overflow-x: hidden;
}

.logoImg {
  width: 100px;
  height: auto;
  display: block;
}

.menuIconTopRight {
  width: 16px;
  height: 16px;
  object-fit: contain;
  cursor: pointer;
  display: block;
}

.heroContentWrapper {
  position: absolute;
  bottom: 7rem;
  left: 5rem;
  transform: none;
  max-width: 900px;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  font-family: 'Inter', Arial, Helvetica, sans-serif;
  pointer-events: none;
}

.heroHeading {
  color: #fff;
  font-family: 'Inter', Arial, Helvetica, sans-serif;
  font-size: 4rem;
  font-weight: 600;
  line-height: 1.25;
  text-align: left;
  margin-bottom: 2rem;
}

.heroButton {
  padding: 0.75rem 1.5rem;
  font-size: 0.90rem;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
  font-weight: 400;
  color: #000000;
  background: #ffffff;
  border: none;
  cursor: pointer;
  align-self: flex-start;
  pointer-events: auto;
}

.heroText1 {
  position: absolute;
  top: 2.5rem;
  left: 30rem;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
  font-weight: 300;
  font-size: 0.75rem;
  color: #6e6e6e;
  line-height: 1.4;
}

.heroText2 {
  position: absolute;
  top: 2.5rem;
  left: 58rem;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
  font-weight: 300;
  font-size: 0.75rem;
  color: #6e6e6e;
  line-height: 1.4;
}

.heroText3 {
  position: absolute;
  top: 2.5rem;
  right: 30rem;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
  font-weight: 300;
  font-size: 0.75rem;
  color: #6e6e6e;
  line-height: 1.4;
}

.heroText4 {
  position: absolute;
  top: 20rem;
  left: 15rem;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
  font-weight: 300;
  font-size: 0.75rem;
  color: #6e6e6e;
  line-height: 1.4;
}

.heroText5 {
  position: absolute;
  top: 25rem;
  right: 15rem;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
  font-weight: 300;
  font-size: 0.75rem;
  color: #6e6e6e;
  line-height: 1.4;
}

.heroText6 {
  position: absolute;
  bottom: 3rem;
  right: 25rem;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
  font-weight: 300;
  font-size: 0.75rem;
  color: #6e6e6e;
  line-height: 1.4;
}

.heroText7 {
  position: absolute;
  bottom: 3rem;
  right: 5rem;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
  font-weight: 300;
  font-size: 0.75rem;
  color: #6e6e6e;
  line-height: 1.4;
}

.subContentWrapper {
  position: absolute;
  right: 5rem;
  bottom: 10rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1.5rem;
  max-width: 450px;
  pointer-events: none;
}

.subText {
  color: #ffffff;
  font-size: 1rem;
  margin-bottom: 0;
  font-weight: 400;
  line-height: 1.5;
  font-family: 'Inter', Arial, Helvetica, sans-serif;
  pointer-events: none;
}

.subButton {
  padding: 0.75rem 1.5rem;
  font-size: 0.90rem;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
  font-weight: 400;
  color: #000000;
  background: #ffffff;
  border: none;
  cursor: pointer;
  align-self: flex-start;
  pointer-events: auto;
}

.scrollIndicator {
  position: absolute;
  bottom: 3rem;
  left: 5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  pointer-events: auto;
}

.scrollText {
  color: #6e6e6e;
  font-size: 0.75rem;
  letter-spacing: 0.1em;
  font-weight: 300;
  margin: 0;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
}

.scrollArrow {
  width: 32px;
  height: auto;
}

/* Center Content - Appears at center position */
.centerContent {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 15;
  opacity: 1;
  pointer-events: auto;
  width: auto;
  height: auto;
}

.centerContentWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 600px;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.centerHeading {
  color: #ffffff;
  font-family: 'Inter', Arial, Helvetica, sans-serif;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  text-align: center;
}

.centerSubtext {
  color: #ffffff;
  font-family: 'Inter', Arial, Helvetica, sans-serif;
  font-size: 1.1rem;
  font-weight: 400;
  line-height: 1.6;
  margin-bottom: 2rem;
  text-align: center;
  opacity: 0.9;
}

.centerButton {
  padding: 1rem 2rem;
  font-size: 0.95rem;
  font-family: 'Supply', Arial, Helvetica, sans-serif;
  font-weight: 500;
  color: #000000;
  background: #ffffff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  pointer-events: auto;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.centerButton:hover {
  background: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}
