import React, { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { useControls } from 'leva';
import styles from './Scene1Overlay.module.css';

function Scene1Overlay({ style, scrollProgress = 0 }) {
  const overlayRef = useRef(null);
  const centerContentRef = useRef(null);

  // Leva controls for testing animation values
  const {
    animationThreshold,
    maxBlur,
    enableAnimation,
    testProgress,
    centerAppearThreshold,
    centerBlurAmount
  } = useControls('Scene1 Overlay Animation', {
    enableAnimation: true,
    animationThreshold: { value: 0.25, min: 0.1, max: 1, step: 0.01 },
    maxBlur: { value: 10, min: 0, max: 20, step: 1 },
    testProgress: { value: 0, min: 0, max: 1, step: 0.01 },
    centerAppearThreshold: { value: 0.55, min: 0.3, max: 0.8, step: 0.01 },
    centerBlurAmount: { value: 8, min: 0, max: 15, step: 1 }
  });

  // Main overlay animation (hero content fade out)
  useEffect(() => {
    if (!overlayRef.current || !enableAnimation) return;

    // Use test progress if available, otherwise use scroll progress
    const currentProgress = testProgress > 0 ? testProgress : scrollProgress;

    // Calculate animation progress based on scroll
    // Full animation should complete after the specified threshold
    const animationProgress = Math.min(currentProgress / animationThreshold, 1); // 0 to 1

    // Apply blur and opacity animations using GSAP
    gsap.to(overlayRef.current, {
      duration: 0.1, // Fast response to scroll
      ease: "none", // Linear animation for smooth scroll response
      filter: `blur(${animationProgress * maxBlur}px)`, // 0px to maxBlur blur
      opacity: 1 - animationProgress, // 1 to 0 opacity
      overwrite: true // Overwrite any existing animations
    });

  }, [scrollProgress, animationThreshold, maxBlur, enableAnimation, testProgress]);

  // Center content animation (appears at center position)
  useEffect(() => {
    if (!centerContentRef.current || !enableAnimation) return;

    // Use test progress if available, otherwise use scroll progress
    const currentProgress = testProgress > 0 ? testProgress : scrollProgress;

    // Center content animation phases:
    // 1. Invisible until 5% before center (55% scroll progress)
    // 2. Fade in with blur from 55% to 60% (center)
    // 3. Full opacity and sharp at 60% (center)
    // 4. Fade out with blur from 70% to 100%

    let centerOpacity = 0;
    let centerBlur = centerBlurAmount;

    if (currentProgress < centerAppearThreshold) {
      // Phase 1: Invisible
      centerOpacity = 0;
      centerBlur = centerBlurAmount;
    } else if (currentProgress < 0.60) {
      // Phase 2: Fade in approaching center (55% to 60%)
      const fadeInProgress = (currentProgress - centerAppearThreshold) / (0.60 - centerAppearThreshold);
      centerOpacity = fadeInProgress * 0.3; // Low opacity
      centerBlur = centerBlurAmount * (1 - fadeInProgress);
    } else if (currentProgress <= 0.70) {
      // Phase 3: Full visibility at center (60% to 70%)
      centerOpacity = 1;
      centerBlur = 0;
    } else {
      // Phase 4: Fade out leaving center (70% to 100%)
      const fadeOutProgress = (currentProgress - 0.70) / 0.30;
      centerOpacity = 1 - fadeOutProgress;
      centerBlur = fadeOutProgress * centerBlurAmount;
    }

    // Apply center content animations using GSAP
    gsap.to(centerContentRef.current, {
      duration: 0.1,
      ease: "none",
      opacity: centerOpacity,
      filter: `blur(${centerBlur}px)`,
      overwrite: true
    });

  }, [scrollProgress, enableAnimation, testProgress, centerAppearThreshold, centerBlurAmount]);

  return (
    <div
      ref={overlayRef}
      className={styles.overlay}
      style={{ pointerEvents: 'none', opacity: 1, ...style }}
    >
      <div className={styles.topNavBar}>
        <img src="/blcks_logo_icon.svg" alt="BLCKS Logo" className={styles.logoImg} />
        <img src="/menu_icon.svg" alt="Menu Icon" className={styles.menuIconTopRight} />
      </div>
      <div className={styles.heroContentWrapper}>
        <h1 className={styles.heroHeading}>
          Wir entwickeln leistungsstarke KI-Systeme für skalierbares Wachstum
        </h1>
      </div>
      {/* 6 absolutely positioned hero texts inside overlay */}
      <div className={styles.heroText1}>BLCK.LABS V01<br />GRAZ. AUT<br />NUM ID : A420</div>
      <div className={styles.heroText2}>AI.RESEARCH<br />LAB 10</div>
      <div className={styles.heroText3}>PROFIL #01</div>
      <div className={styles.heroText4}>BLCKS V01<br />REFERENCE ID 2308106</div>
      <div className={styles.heroText5}>AI DEVELOPMENT<br />STUDIO COMPANY</div>
      <div className={styles.heroText6}>WEB RENDERED v0.2.4</div>
      <div className={styles.heroText7}>KEY GENERATION v0.1.2</div>
      <div className={styles.subContentWrapper}>
        <div className={styles.subText}>
          Wir entwickeln, optimieren und skalieren intelligente Automatisierungslösungen, die Prozesse verschlanken, Effizienz steigern und den ROI maximieren.
        </div>
        <button className={styles.subButton}>ENTDECKE UNSERE BLCKS</button>
      </div>
      <div className={styles.scrollIndicator}>
        <p className={styles.scrollText}>SCROLL DOWN</p>
        <img src="/scrolldownpfeil_grey.svg" alt="Scroll Down" className={styles.scrollArrow} />
      </div>

      {/* Center Content - Appears at center position */}
      <div ref={centerContentRef} className={styles.centerContent}>
        <div className={styles.centerContentWrapper}>
          <h2 className={styles.centerHeading}>
            Entdecke die Zukunft der KI-Entwicklung
          </h2>
          <p className={styles.centerSubtext}>
            Unsere fortschrittlichen Algorithmen und maßgeschneiderten Lösungen bringen dein Unternehmen auf das nächste Level der digitalen Transformation.
          </p>
          <button className={styles.centerButton}>MEHR ERFAHREN</button>
        </div>
      </div>
    </div>
  );
}

export default Scene1Overlay;
