import React from 'react';
import styles from '../../sections/HeroSection.module.css';

function Scene1Overlay({ style }) {
  return (
    <div
      className={styles.overlay}
      style={{ pointerEvents: 'none', opacity: 1, ...style }}
    >
      <div className={styles.topNavBar}>
        <img src="/blcks_logo_icon.svg" alt="BLCKS Logo" className={styles.logoImg} />
        <img src="/menu_icon.svg" alt="Menu Icon" className={styles.menuIconTopRight} />
      </div>
      <div className={styles.heroContentWrapper}>
        <h1 className={styles.heroHeading}>
          Wir entwickeln leistungsstarke<br />
          KI-Systeme für skalierbares<br />
          Wachstum
        </h1>
        <button className={styles.heroButton}>EXPLORE OUR SERVICES</button>
      </div>
      {/* 6 absolutely positioned hero texts inside overlay */}
      <div className={styles.heroText1}>BLCK.LABS V01<br />GRAZ. AUT<br />NUM ID : A420</div>
      <div className={styles.heroText2}>AI.RESEARCH<br />LAB 10</div>
      <div className={styles.heroText3}>PROFIL #01</div>
      <div className={styles.heroText4}>BLCKS V01<br />REFERENCE ID 2308106</div>
      <div className={styles.heroText5}>AI DEVELOPMENT<br />STUDIO COMPANY</div>
      <div className={styles.heroText6}>WEB RENDERED v0.2.4</div>
      <div className={styles.heroText7}>KEY GENERATION v0.1.2</div>
      <div className={styles.subText}>
        Wir entwickeln, optimieren und skalieren intelligente Automatisierungslösungen, die Prozesse verschlanken, Effizienz steigern und den ROI maximieren.
      </div>
      <div className={styles.scrollIndicator}>
        <p className={styles.scrollText}>SCROLL DOWN</p>
        <img src="/scrolldownpfeil_white.svg" alt="Scroll Down" className={styles.scrollArrow} />
      </div>
    </div>
  );
}

export default Scene1Overlay;
